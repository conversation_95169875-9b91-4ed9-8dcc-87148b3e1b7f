version: '3.8'

services:
  # Pandoc service using official pandoc/core image
  pandoc:
    image: pandoc/core:latest
    container_name: pandoc-service
    volumes:
      - pandoc_shared:/shared
    # Keep container running for service calls
    command: tail -f /dev/null
    restart: unless-stopped
    networks:
      - pandoc-network

  # Main FastAPI application
  pandoc-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile.service
    container_name: pandoc-api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/pandoc_api.db
      - UPLOAD_DIR=storage/uploads
      - OUTPUT_DIR=storage/outputs
      - TEMPLATE_DIR=templates
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - DEBUG=false
      - PANDOC_TIMEOUT=300
      - MAX_FILE_SIZE=104857600  # 100MB
      - SECRET_KEY=your-secret-key-change-in-production
      # Pandoc service configuration
      - PANDOC_SERVICE_HOST=pandoc
      - PANDOC_SERVICE_ENABLED=true
    volumes:
      - pandoc_data:/app/data
      - pandoc_storage:/app/storage
      - pandoc_templates:/app/templates
      - pandoc_shared:/shared
    depends_on:
      - pandoc
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pandoc-network

volumes:
  pandoc_data:
    driver: local
  pandoc_storage:
    driver: local
  pandoc_templates:
    driver: local
  pandoc_shared:
    driver: local

networks:
  pandoc-network:
    driver: bridge
